<mujocoinclude>
    <compiler angle="radian" inertiafromgeom="auto" inertiagrouprange="4 5"/>
    <asset>

      <material name="window_col" rgba="0.3 0.3 1.0 0.5" shininess="0" specular="0"/>
      <material name="window_white" rgba=".65 .65 .65 1" shininess="1" reflectance=".7" specular=".5"/>
      <material name="window_red" rgba=".36 .26 .27 1" shininess="1" reflectance=".7" specular=".5"/>
      <material name="window_green" rgba=".51 .58 .55 1" shininess="1" reflectance=".7" specular=".5"/>
      <material name="window_black" rgba=".3 .3 .3 1" shininess="1" reflectance=".7" specular=".5"/>
      <material name="window_glass" rgba="0 .3 .4 .1" shininess="1" reflectance="1" specular=".5"/>

    </asset>
    <default>
      <default class="window_base">
          <joint armature="0.001" damping="2" limited="true"/>
          <geom conaffinity="0" contype="0" group="1" type="mesh"/>
          <position ctrllimited="true" ctrlrange="0 1.57"/>
          <default class="window_viz">
              <geom condim="4" type="mesh"/>
          </default>
          <default class="window_col">
              <geom conaffinity="1" condim="3" contype="1" group="4" material="window_col" solimp="0.99 0.99 0.01" solref="0.01 1"/>
          </default>
      </default>
    </default>

    <asset>
      <mesh file="../objects/meshes/window/window_h_base.stl" name="window_h_base"/>
      <mesh file="../objects/meshes/window/window_h_frame.stl" name="window_h_frame"/>
      <mesh file="../objects/meshes/window/windowa_h_frame.stl" name="windowa_h_frame"/>
      <mesh file="../objects/meshes/window/windowa_h_glass.stl" name="windowa_h_glass"/>
      <mesh file="../objects/meshes/window/windowb_h_frame.stl" name="windowb_h_frame"/>
      <mesh file="../objects/meshes/window/windowb_h_glass.stl" name="windowb_h_glass"/>

      <mesh file="../objects/meshes/window/window_base.stl" name="window_base"/>
      <mesh file="../objects/meshes/window/window_frame.stl" name="window_frame"/>
      <mesh file="../objects/meshes/window/windowa_frame.stl" name="windowa_frame"/>
      <mesh file="../objects/meshes/window/windowa_glass.stl" name="windowa_glass"/>
      <mesh file="../objects/meshes/window/windowb_frame.stl" name="windowb_frame"/>
      <mesh file="../objects/meshes/window/windowb_glass.stl" name="windowb_glass"/>
    </asset>

</mujocoinclude>

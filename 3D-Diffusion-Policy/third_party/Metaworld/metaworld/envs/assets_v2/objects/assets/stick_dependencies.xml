<mujocoinclude>
    <compiler angle="radian" inertiafromgeom="auto" inertiagrouprange="4 5"/>
    <asset>
      <material name="stick_col" rgba="0.3 0.3 1.0 0.5" shininess="0" specular="0"/>
      <material name="stick_blue" rgba=".15 .3 .5 1" shininess="1" reflectance=".7" specular=".5"/>

    </asset>
    <default>
      <default class="stick_base">
          <joint armature="0.001" damping="2" limited="true"/>
          <geom conaffinity="0" contype="0" group="1" type="mesh"/>
          <position ctrllimited="true" ctrlrange="0 1.57"/>
          <default class="stick_col">
              <geom conaffinity="1" condim="4" contype="1" group="4" material="stick_col" solimp="0.998 0.998 0.001" solref="0.02 1"/>
          </default>
      </default>
    </default>

    <asset>
      <mesh file="../objects/meshes/stick/stick.stl" name="stick"/>
    </asset>

</mujocoinclude>

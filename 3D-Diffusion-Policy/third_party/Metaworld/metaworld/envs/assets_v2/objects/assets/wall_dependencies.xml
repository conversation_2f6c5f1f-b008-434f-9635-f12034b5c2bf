<mujocoinclude>
    <compiler angle="radian" inertiafromgeom="auto" inertiagrouprange="4 5"/>
    <asset>
        <texture name="T_wallbrick" type="cube" file="../textures/brick1.png"/>
      <material name="wall_col" rgba="0.3 0.3 1.0 0.5" shininess="0" specular="0"/>
      <material name="wall_brick" texture="T_wallbrick" shininess="1" reflectance=".7" specular=".5"/>
    </asset>
    <default>
      <default class="wall_base">
          <joint armature="0.001" damping="2" limited="true"/>
          <geom conaffinity="0" contype="0" group="1" type="mesh"/>
          <default class="wall_viz">
              <geom condim="4" type="mesh"/>
          </default>
          <default class="wall_col">
              <geom conaffinity="1" condim="3" contype="0" group="4" material="wall_col" solimp="0.99 0.99 0.01" solref="0.01 1"/>
          </default>

      </default>
    </default>

</mujocoinclude>
